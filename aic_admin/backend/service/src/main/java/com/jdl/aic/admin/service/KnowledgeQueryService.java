package com.jdl.aic.admin.service;

import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;

/**
 * 知识库查询服务（业务聚合层，调用KnowledgeService）
 */
public interface KnowledgeQueryService {
    /**
     * 查询知识内容列表（分页）
     * @param pageRequest 分页参数
     * @param knowledgeTypeCode 知识类型编码
     * @param status 状态
     * @param authorId 作者ID
     * @param teamId 团队ID
     * @param search 关键词
     * @return 分页知识内容列表
     */
    Result<PageResult<KnowledgeDTO>> getKnowledgeList(
            PageRequest pageRequest,
            String knowledgeTypeCode,
            Integer status,
            Long authorId,
            Long teamId,
            String search
    );

    KnowledgeDTO getKnowledgeById(Long id);

    void deleteKnowledgeById(Long id);

    void createKnowledge(KnowledgeDTO knowledge);
}
