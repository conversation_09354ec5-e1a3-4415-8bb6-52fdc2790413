package com.jdl.aic.admin.service;

import com.jdl.aic.admin.common.result.Result;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;

import java.util.List;

/**
 * 解决方案管理服务接口（业务聚合层，调用aic_core_service的SolutionService）
 * 
 * <p>该服务采用JSF API模式，直接调用底层SolutionService，不使用数据库DAO层。
 * 参考KnowledgeQueryServiceImpl的实现模式。
 */
public interface SolutionService {

    /**
     * 查询解决方案列表（分页）
     * 
     * @param pageRequest 分页参数
     * @param category 分类过滤（可选）
     * @param status 状态过滤（可选）
     * @param authorId 作者ID过滤（可选）
     * @param search 搜索关键词（可选）
     * @return 分页解决方案列表
     */
    Result<PageResult<SolutionDTO>> getSolutionList(PageRequest pageRequest,
                                                   String category,
                                                   Integer status,
                                                   Long authorId,
                                                   String search);

    /**
     * 根据ID获取解决方案详情
     * 
     * @param id 解决方案ID
     * @return 解决方案详情
     */
    SolutionDTO getSolutionById(Long id);

    /**
     * 创建解决方案
     * 
     * @param solution 解决方案信息
     * @return 创建结果
     */
    Result<SolutionDTO> createSolution(SolutionDTO solution);

    /**
     * 更新解决方案
     * 
     * @param id 解决方案ID
     * @param solution 解决方案信息
     * @return 更新结果
     */
    Result<SolutionDTO> updateSolution(Long id, SolutionDTO solution);

    /**
     * 删除解决方案
     * 
     * @param id 解决方案ID
     * @return 删除结果
     */
    Result<Void> deleteSolution(Long id);

    /**
     * 批量删除解决方案
     * 
     * @param ids 解决方案ID列表
     * @return 删除结果
     */
    Result<Void> batchDeleteSolutions(List<Long> ids);

    /**
     * 批量更新解决方案状态
     * 
     * @param ids 解决方案ID列表
     * @param status 目标状态
     * @return 更新结果
     */
    Result<Void> batchUpdateStatus(List<Long> ids, Integer status);



    // ==================== 统计和分析 ====================

    /**
     * 获取解决方案分类统计
     * 
     * @return 分类统计信息
     */
    Result<List<Object>> getSolutionCategoryStats();

    /**
     * 获取热门解决方案列表
     * 
     * @param pageRequest 分页参数
     * @param category 分类过滤（可选）
     * @param days 统计天数（可选，默认30天）
     * @return 热门解决方案列表
     */
    Result<PageResult<SolutionDTO>> getPopularSolutions(PageRequest pageRequest,
                                                        String category,
                                                        Integer days);
}
