package com.jdl.aic.admin.service.impl;

import com.jdl.aic.admin.service.KnowledgeQueryService;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 知识库查询服务实现
 */
@Service
public class KnowledgeQueryServiceImpl implements KnowledgeQueryService {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeQueryServiceImpl.class);

    @Resource
    private final KnowledgeService knowledgeService;

    @Autowired(required = false)
    public KnowledgeQueryServiceImpl(KnowledgeService knowledgeService) {
        this.knowledgeService = knowledgeService;
    }

    /**
     * 查询知识内容列表（分页）
     */
    @Override
    public Result<PageResult<KnowledgeDTO>> getKnowledgeList(PageRequest pageRequest,
                                                             String knowledgeTypeCode,
                                                             Integer status,
                                                             Long authorId,
                                                             Long teamId,
                                                             String search) {
        try {
            // 调用底层 KnowledgeService
            Result<PageResult<KnowledgeDTO>> result = knowledgeService.getKnowledgeList(
                    pageRequest, knowledgeTypeCode, status, authorId, teamId, search);

            // 记录调用结果
            if (result != null && result.isSuccess()) {
                PageResult<KnowledgeDTO> pageResult = result.getData();
                if (pageResult != null) {
                    // 尝试获取数据列表数量
                    try {
                        Object dataObj = pageResult.getClass().getMethod("getData").invoke(pageResult);
                        if (dataObj instanceof List) {
                            List<?> dataList = (List<?>) dataObj;
                            logger.info("getKnowledgeList 调用成功: 返回 {} 条记录, 类型={}",
                                       dataList.size(), knowledgeTypeCode);
                        }
                    } catch (Exception e) {
                        logger.info("getKnowledgeList 调用成功: 类型={}", knowledgeTypeCode);
                    }
                } else {
                    logger.info("getKnowledgeList 调用成功: 返回数据为空, 类型={}", knowledgeTypeCode);
                }
            } else {
                String message = result != null ? result.getMessage() : "返回结果为null";
                logger.warn("getKnowledgeList 调用失败: {}, 类型={}", message, knowledgeTypeCode);
            }

            return result;

        } catch (Exception e) {
            logger.error("getKnowledgeList 调用异常: {}, 类型={}", e.getMessage(), knowledgeTypeCode);
            throw new RuntimeException("查询知识列表时发生异常: " + e.getMessage(), e);
        }
    }

    @Override
    public KnowledgeDTO getKnowledgeById(Long id) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                logger.warn("getKnowledgeById 参数无效: id={}", id);
                return null;
            }

            // 调用底层 KnowledgeService
            Result<KnowledgeDTO> result = knowledgeService.getKnowledgeById(id);

            // 记录调用结果
            if (result != null && result.isSuccess()) {
                KnowledgeDTO knowledge = result.getData();
                if (knowledge != null) {
                    logger.info("getKnowledgeById 调用成功: ID={}, 标题='{}'", knowledge.getId(), knowledge.getTitle());
                } else {
                    logger.warn("getKnowledgeById 调用成功但数据为空: ID={}", id);
                }
                return knowledge;
            } else {
                String message = result != null ? result.getMessage() : "返回结果为null";
                logger.warn("getKnowledgeById 调用失败: ID={}, 原因={}", id, message);
                return null;
            }

        } catch (Exception e) {
            logger.error("getKnowledgeById 调用异常: ID={}, 错误={}", id, e.getMessage());
            throw new RuntimeException("根据ID查询知识时发生异常: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteKnowledgeById(Long id) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                logger.warn("deleteKnowledgeById 参数无效: id={}", id);
                throw new IllegalArgumentException("知识ID不能为空或无效");
            }

            // 调用底层 KnowledgeService
            Result<Void> result = knowledgeService.deleteKnowledge(id);

            // 记录调用结果
            if (result != null && result.isSuccess()) {
                logger.info("deleteKnowledgeById 调用成功: ID={}", id);
            } else {
                String message = result != null ? result.getMessage() : "返回结果为null";
                logger.warn("deleteKnowledgeById 调用失败: ID={}, 原因={}", id, message);
                throw new RuntimeException("删除知识失败: " + message);
            }

        } catch (Exception e) {
            logger.error("deleteKnowledgeById 调用异常: ID={}, 错误={}", id, e.getMessage());
            throw new RuntimeException("删除知识时发生异常: " + e.getMessage(), e);
        }
    }

    @Override
    public void createKnowledge(KnowledgeDTO knowledge) {
        Result<KnowledgeDTO> result = knowledgeService.createKnowledge(knowledge);
        logger.info("创建知识结果:{}",result.getCode());
    }
}
