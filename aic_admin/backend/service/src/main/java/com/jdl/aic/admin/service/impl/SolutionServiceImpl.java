package com.jdl.aic.admin.service.impl;

import com.jdl.aic.admin.common.result.Result;
import com.jdl.aic.admin.service.SolutionService;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 解决方案管理服务实现类
 *
 * <p>采用JSF API模式，直接调用aic_core_service的SolutionService，
 * 不使用数据库DAO层。参考KnowledgeQueryServiceImpl的实现模式。
 */
@Service
public class SolutionServiceImpl implements SolutionService {

    private final com.jdl.aic.core.service.client.service.SolutionService coreSolutionService;

    @Autowired(required = false)
    public SolutionServiceImpl(com.jdl.aic.core.service.client.service.SolutionService coreSolutionService) {
        this.coreSolutionService = coreSolutionService;
    }

    @Override
    public Result<PageResult<SolutionDTO>> getSolutionList(PageRequest pageRequest,
                                                           String category,
                                                           Integer status,
                                                           Long authorId,
                                                           String search) {
        try {
            // 直接调用底层 SolutionService
            com.jdl.aic.core.service.client.common.Result<PageResult<SolutionDTO>> coreResult =
                    coreSolutionService.getSolutionList(pageRequest, category, status, authorId, search);

            if (coreResult.isSuccess()) {
                return Result.success(coreResult.getData());
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("获取解决方案列表失败：" + e.getMessage());
        }
    }

    @Override
    public SolutionDTO getSolutionById(Long id) {
        try {
            com.jdl.aic.core.service.client.common.Result<SolutionDTO> coreResult =
                    coreSolutionService.getSolutionById(id);
            return coreResult.isSuccess() ? coreResult.getData() : null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Result<SolutionDTO> createSolution(SolutionDTO solution) {
        try {
            com.jdl.aic.core.service.client.common.Result<SolutionDTO> coreResult =
                    coreSolutionService.createSolution(solution);

            if (coreResult.isSuccess()) {
                return Result.success(coreResult.getData());
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("创建解决方案失败：" + e.getMessage());
        }
    }

    @Override
    public Result<SolutionDTO> updateSolution(Long id, SolutionDTO solution) {
        try {
            com.jdl.aic.core.service.client.common.Result<SolutionDTO> coreResult =
                    coreSolutionService.updateSolution(id, solution);

            if (coreResult.isSuccess()) {
                return Result.success(coreResult.getData());
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("更新解决方案失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteSolution(Long id) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> coreResult =
                    coreSolutionService.deleteSolution(id);

            if (coreResult.isSuccess()) {
                return Result.success();
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("删除解决方案失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchDeleteSolutions(List<Long> ids) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> coreResult = null;
            for (Long id : ids) {
                coreResult = coreSolutionService.deleteSolution(id);
                if (!coreResult.isSuccess()) {
                    break;
                }
            }
            if (coreResult.isSuccess()) {
                return Result.success();
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("批量删除解决方案失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateStatus(List<Long> ids, Integer status) {
        try {
            com.jdl.aic.core.service.client.common.Result<Void> coreResult = null;
            for (Long id : ids) {
                coreResult =
                        coreSolutionService.updateSolutionStatus(id, status);
                if (!coreResult.isSuccess()) {
                    break;
                }
            }

            if (coreResult.isSuccess()) {
                return Result.success();
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("批量更新状态失败：" + e.getMessage());
        }
    }


    // ==================== 统计和分析 ====================

    @Override
    public Result<List<Object>> getSolutionCategoryStats() {
        try {
            com.jdl.aic.core.service.client.common.Result<List<Object>> coreResult =
                    coreSolutionService.getSolutionCategoryStats();

            if (coreResult.isSuccess()) {
                return Result.success(coreResult.getData());
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("获取分类统计失败：" + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<SolutionDTO>> getPopularSolutions(PageRequest pageRequest,
                                                               String category,
                                                               Integer days) {
        try {
            com.jdl.aic.core.service.client.common.Result<PageResult<SolutionDTO>> coreResult =
                    coreSolutionService.getPopularSolutions(pageRequest, category, days);

            if (coreResult.isSuccess()) {
                return Result.success(coreResult.getData());
            } else {
                return Result.failed(coreResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("获取热门解决方案失败：" + e.getMessage());
        }
    }
}
