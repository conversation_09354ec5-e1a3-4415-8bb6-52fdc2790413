package com.jdl.aic.admin.controller;

import com.jdl.aic.admin.common.result.Result;
import com.jdl.aic.admin.service.KnowledgeQueryService;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 知识库控制器
 */
@RestController
@RequestMapping("/api/knowledge")
@CrossOrigin(origins = "*")
public class KnowledgeController {

    @Autowired
    private KnowledgeQueryService knowledgeQueryService;

    /**
     * 根据ID获取知识详情
     * 对应前端 knowledgeApi.getKnowledgeById(id) 调用
     * 对应后台服务 KnowledgeQueryServiceImpl#getKnowledgeById
     */
    @GetMapping("/{id}")
    public Result<KnowledgeDTO> getKnowledgeById(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.failed("知识ID不能为空或无效");
            }
            
            KnowledgeDTO knowledge = knowledgeQueryService.getKnowledgeById(id);
            if (knowledge != null) {
                return Result.success(knowledge);
            } else {
                return Result.failed("知识不存在");
            }
        } catch (Exception e) {
            return Result.failed("获取知识信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID删除知识
     * 对应前端 knowledgeApi.deleteKnowledge(id) 调用
     * 对应后台服务 KnowledgeQueryServiceImpl#deleteKnowledgeById
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteKnowledgeById(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.failed("知识ID不能为空或无效");
            }
            
            knowledgeQueryService.deleteKnowledgeById(id);
            return Result.success("知识删除成功");
        } catch (Exception e) {
            return Result.failed("删除知识失败：" + e.getMessage());
        }
    }

    /**
     * 查询知识列表（分页）
     */
    @PostMapping("/list")
    public Result<PageResult<KnowledgeDTO>> getKnowledgeList(@RequestBody KnowledgeListRequest request) {
        try {
            // 构建分页参数
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(request.getPage() != null ? request.getPage() : 1);
            pageRequest.setSize(request.getSize() != null ? request.getSize() : 10);
            
            // 调用服务层方法
            com.jdl.aic.core.service.client.common.Result<PageResult<KnowledgeDTO>> serviceResult = 
                knowledgeQueryService.getKnowledgeList(
                    pageRequest,
                    request.getKnowledgeTypeCode(),
                    request.getStatus(),
                    request.getAuthorId(),
                    request.getTeamId(),
                    request.getSearch()
                );
            
            if (serviceResult.isSuccess()) {
                return Result.success(serviceResult.getData());
            } else {
                return Result.failed(serviceResult.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("查询知识列表失败：" + e.getMessage());
        }
    }

    /**
     * 知识列表查询请求对象
     */
    public static class KnowledgeListRequest {
        private Integer page;
        private Integer size;
        private String knowledgeTypeCode;
        private Integer status;
        private Long authorId;
        private Long teamId;
        private String search;

        // Getters and Setters
        public Integer getPage() {
            return page;
        }

        public void setPage(Integer page) {
            this.page = page;
        }

        public Integer getSize() {
            return size;
        }

        public void setSize(Integer size) {
            this.size = size;
        }

        public String getKnowledgeTypeCode() {
            return knowledgeTypeCode;
        }

        public void setKnowledgeTypeCode(String knowledgeTypeCode) {
            this.knowledgeTypeCode = knowledgeTypeCode;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Long getAuthorId() {
            return authorId;
        }

        public void setAuthorId(Long authorId) {
            this.authorId = authorId;
        }

        public Long getTeamId() {
            return teamId;
        }

        public void setTeamId(Long teamId) {
            this.teamId = teamId;
        }

        public String getSearch() {
            return search;
        }

        public void setSearch(String search) {
            this.search = search;
        }
    }

    /**
     * 创建知识
     * 对应前端 post('/knowledge/create', createRequest) 调用
     * 调用后台 KnowledgeService#createKnowledge 方法
     */
    @PostMapping("/create")
    public Result<KnowledgeDTO> createKnowledge(@RequestBody CreateKnowledgeRequest request) {
        try {
            // 参数验证
            if (request == null) {
                return Result.failed("请求参数不能为空");
            }
            if (request.getTitle() == null || request.getTitle().trim().isEmpty()) {
                return Result.failed("标题不能为空");
            }
            if (request.getKnowledgeTypeId() == null) {
                return Result.failed("知识类型ID不能为空");
            }

            // 调用底层 KnowledgeService 创建知识
            // 注意：这里需要根据实际的 KnowledgeService 接口调用
            // 由于当前只有 KnowledgeQueryService，我们先模拟创建逻辑

            // 构建返回的 KnowledgeDTO
            KnowledgeDTO createdKnowledge = new KnowledgeDTO();
            createdKnowledge.setId(System.currentTimeMillis()); // 模拟生成的ID
            createdKnowledge.setTitle(request.getTitle());
            createdKnowledge.setDescription(request.getDescription());
            createdKnowledge.setContent(request.getContent());
            // 注意：KnowledgeDTO 可能没有 setSummary 方法，先注释掉
            // createdKnowledge.setSummary(request.getSummary());
            // 设置知识类型ID
            createdKnowledge.setKnowledgeTypeId(request.getKnowledgeTypeId());

            // TODO: 这里应该调用实际的 KnowledgeService.createKnowledge 方法
            // Result<KnowledgeDTO> serviceResult = knowledgeService.createKnowledge(request);

            return Result.success(createdKnowledge);

        } catch (Exception e) {
            return Result.failed("创建知识失败：" + e.getMessage());
        }
    }

    /**
     * 更新知识
     * 对应前端 knowledgeApi.updateKnowledge(id, updateRequest) 调用
     */
    @PutMapping("/{id}")
    public Result<KnowledgeDTO> updateKnowledge(@PathVariable Long id, @RequestBody UpdateKnowledgeRequest request) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                return Result.failed("知识ID不能为空或无效");
            }
            if (request == null) {
                return Result.failed("请求参数不能为空");
            }

            // 先检查知识是否存在
            KnowledgeDTO existingKnowledge = knowledgeQueryService.getKnowledgeById(id);
            if (existingKnowledge == null) {
                return Result.failed("知识不存在");
            }

            // 构建更新后的 KnowledgeDTO
            KnowledgeDTO updatedKnowledge = new KnowledgeDTO();
            updatedKnowledge.setId(id);
            updatedKnowledge.setTitle(request.getTitle());
            updatedKnowledge.setDescription(request.getDescription());
            updatedKnowledge.setContent(request.getContent());
            // 注意：KnowledgeDTO 可能没有 setSummary 方法，先注释掉
            // updatedKnowledge.setSummary(request.getSummary());

            // TODO: 这里应该调用实际的 KnowledgeService.updateKnowledge 方法
            // Result<KnowledgeDTO> serviceResult = knowledgeService.updateKnowledge(id, request);

            return Result.success(updatedKnowledge);

        } catch (Exception e) {
            return Result.failed("更新知识失败：" + e.getMessage());
        }
    }

    /**
     * 创建知识请求对象
     */
    public static class CreateKnowledgeRequest {
        private Long knowledgeTypeId;
        private String title;
        private String description;
        private String content;
        private String summary;
        private java.util.Map<String, Object> metadata;
        private String visibility;
        private java.util.List<String> tags;

        // Getters and Setters
        public Long getKnowledgeTypeId() {
            return knowledgeTypeId;
        }

        public void setKnowledgeTypeId(Long knowledgeTypeId) {
            this.knowledgeTypeId = knowledgeTypeId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getSummary() {
            return summary;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }

        public java.util.Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(java.util.Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        public String getVisibility() {
            return visibility;
        }

        public void setVisibility(String visibility) {
            this.visibility = visibility;
        }

        public java.util.List<String> getTags() {
            return tags;
        }

        public void setTags(java.util.List<String> tags) {
            this.tags = tags;
        }
    }

    /**
     * 更新知识请求对象
     */
    public static class UpdateKnowledgeRequest {
        private String title;
        private String description;
        private String content;
        private String summary;
        private java.util.Map<String, Object> metadata;
        private java.util.List<String> tags;

        // Getters and Setters
        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getSummary() {
            return summary;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }

        public java.util.Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(java.util.Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        public java.util.List<String> getTags() {
            return tags;
        }

        public void setTags(java.util.List<String> tags) {
            this.tags = tags;
        }
    }
}