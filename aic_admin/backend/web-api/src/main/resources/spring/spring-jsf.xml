<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- 注册中心配置 -->
    <jsf:registry id="jsfRegistry"  protocol="jsfRegistry" index="${jsf.index}"/>

    <!-- KnowledgeService Consumer 配置 -->
    <jsf:consumer id="knowledgeService"
                  interface="com.jdl.aic.core.service.client.service.KnowledgeService"
                  registry="jsfRegistry"
                  alias="aic-test:1.0.0"
                  check="false"
                  timeout="5000"
                  serialization="hessian">
        <jsf:parameter key="token" value="b9fc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:consumer>
    <jsf:consumer id="refCategoryService"
                  interface="com.jdl.aic.core.service.client.service.CategoryService"
                  registry="jsfRegistry"
                  alias="aic-test:1.0.0"
                  check="false"
                  timeout="5000"
                  serialization="hessian">
        <jsf:parameter key="token" value="b9fc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:consumer>

    <!-- SolutionService Consumer 配置 -->
    <jsf:consumer id="solutionService"
                  interface="com.jdl.aic.core.service.client.service.SolutionService"
                  registry="jsfRegistry"
                  alias="aic-test:1.0.0"
                  check="false"
                  timeout="5000"
                  serialization="hessian">
        <jsf:parameter key="token" value="b9fc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:consumer>
</beans>