<template>
  <component :is="iconComponent" :class="iconClass" v-bind="$attrs" />
</template>

<script setup>
import { computed, defineAsyncComponent } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  size: {
    type: [String, Number],
    default: 20
  },
  color: {
    type: String,
    default: 'currentColor'
  }
})

// 图标组件映射
const iconComponents = {
  // 主要功能图标
  home: defineAsyncComponent(() => import('./svg/HomeIcon.vue')),
  dashboard: defineAsyncComponent(() => import('./svg/DashboardIcon.vue')),
  
  // MCP服务图标
  service: defineAsyncComponent(() => import('./svg/ServiceIcon.vue')),
  category: defineAsyncComponent(() => import('./svg/CategoryIcon.vue')),
  tag: defineAsyncComponent(() => import('./svg/TagIcon.vue')),
  
  // 成长地图图标
  growth: defineAsyncComponent(() => import('./svg/GrowthIcon.vue')),
  tools: defineAsyncComponent(() => import('./svg/ToolsIcon.vue')),
  
  // 规则中心图标
  rules: defineAsyncComponent(() => import('./svg/RulesIcon.vue')),
  prompts: defineAsyncComponent(() => import('./svg/PromptsIcon.vue')),
  solution: defineAsyncComponent(() => import('./svg/SolutionIcon.vue')),
  
  // 规范中心图标
  standards: defineAsyncComponent(() => import('./svg/StandardsIcon.vue')),
  content: defineAsyncComponent(() => import('./svg/ContentIcon.vue')),
  
  // AI学习资源图标
  learning: defineAsyncComponent(() => import('./svg/LearningIcon.vue')),
  rss: defineAsyncComponent(() => import('./svg/RssIcon.vue')),
  
  // 定时任务图标
  tasks: defineAsyncComponent(() => import('./svg/TasksIcon.vue')),
  logs: defineAsyncComponent(() => import('./svg/LogsIcon.vue')),
  
  // 控制台图标
  users: defineAsyncComponent(() => import('./svg/UsersIcon.vue')),
  tokens: defineAsyncComponent(() => import('./svg/TokensIcon.vue')),
  usage: defineAsyncComponent(() => import('./svg/UsageIcon.vue')),
  
  // 个人中心图标
  wallet: defineAsyncComponent(() => import('./svg/WalletIcon.vue')),
  bindings: defineAsyncComponent(() => import('./svg/BindingsIcon.vue')),
  settings: defineAsyncComponent(() => import('./svg/SettingsIcon.vue')),
  
  // 系统图标
  search: defineAsyncComponent(() => import('./svg/SearchIcon.vue')),
  chevronDown: defineAsyncComponent(() => import('./svg/ChevronDownIcon.vue')),
  chevronRight: defineAsyncComponent(() => import('./svg/ChevronRightIcon.vue')),
  menu: defineAsyncComponent(() => import('./svg/MenuIcon.vue')),
  close: defineAsyncComponent(() => import('./svg/CloseIcon.vue')),
  external: defineAsyncComponent(() => import('./svg/ExternalIcon.vue')),
  copy: defineAsyncComponent(() => import('./svg/CopyIcon.vue')),
  sun: defineAsyncComponent(() => import('./svg/SunIcon.vue')),
  moon: defineAsyncComponent(() => import('./svg/MoonIcon.vue')),
  logout: defineAsyncComponent(() => import('./svg/LogoutIcon.vue')),
  
  // 状态图标
  notification: defineAsyncComponent(() => import('./svg/NotificationIcon.vue')),
  star: defineAsyncComponent(() => import('./svg/StarIcon.vue')),
  bookmark: defineAsyncComponent(() => import('./svg/BookmarkIcon.vue')),
  clock: defineAsyncComponent(() => import('./svg/ClockIcon.vue'))
}

const iconComponent = computed(() => {
  return iconComponents[props.name] || iconComponents.home
})

const iconClass = computed(() => {
  const sizeClass = typeof props.size === 'number' ? `w-${props.size/4} h-${props.size/4}` : props.size
  return `menu-icon ${sizeClass}`
})
</script>

<style scoped>
.menu-icon {
  transition: all var(--duration-200) var(--ease-out);
  flex-shrink: 0;
}

.menu-icon:hover {
  transform: scale(1.1);
}
</style>
