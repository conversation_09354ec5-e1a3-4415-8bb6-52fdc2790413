import { createRouter, createWebHistory } from "vue-router"
import { useUserStore } from "@/stores/user"
import Dashboard from "@views/dashboard/Dashboard.vue"
import Services from "@views/mcp-service/ServiceList.vue"
import Categories from "@views/category/CategoryList.vue"
import Tags from "@views/tag/TagList.vue"
import Tasks from "@views/task/TaskList.vue"
import Wallet from "@views/wallet/Wallet.vue"
import Settings from "@views/settings/Settings.vue"
import DevTools from "@views/devtools/DevTools.vue"
import Prompts from "@views/prompt/PromptList.vue"
import Content from "@views/content/ContentList.vue"
import RSS from "@views/rss/RSS.vue"
import SidebarDemo from "@views/system/SidebarDemo.vue"
import Login from "@views/system/Login.vue"
import AuthCallback from "@views/system/AuthCallback.vue"
import Users from "@views/user/UserList.vue"
import AccountBindings from "@views/system/AccountBindings.vue"
import OAuth2Test from "@views/system/OAuth2Test.vue"
import Solutions from "@views/solution/SolutionList.vue"
//import McpServiceFormTest from "@views/test/McpServiceFormTest.vue"

const routes = [
  {
    path: "/login",
    name: "login",
    component: Login,
    meta: { title: "用户登录", requiresAuth: false }
  },
  {
    path: "/auth/callback",
    name: "auth-callback",
    component: AuthCallback,
    meta: { title: "登录回调", requiresAuth: false }
  },
  {
    path: "/",
    redirect: "/dashboard",
  },
  {
    path: "/dashboard",
    name: "dashboard",
    component: Dashboard,
    meta: { title: "数据看板", requiresAuth: true }
  },
  {
    path: "/users",
    name: "users",
    component: Users,
    meta: { title: "用户管理", requiresAuth: true }
  },
  {
    path: "/account-bindings",
    name: "account-bindings",
    component: AccountBindings,
    meta: { title: "账号绑定", requiresAuth: true }
  },
  {
    path: "/oauth2-test",
    name: "oauth2-test",
    component: OAuth2Test,
    meta: { title: "OAuth2测试" }
  },
  {
    path: "/menu-demo",
    name: "menu-demo",
    component: () => import("@views/demo/MenuDemo.vue"),
    meta: { title: "菜单演示", requiresAuth: true }
  },
  {
    path: "/services",
    name: "services",
    component: Services,
    meta: { title: "服务管理", requiresAuth: true }
  },
  {
    path: "/categories",
    name: "categories",
    component: Categories,
    meta: { title: "分类管理", requiresAuth: true }
  },
  {
    path: "/tags",
    name: "tags",
    component: Tags,
    meta: { title: "标签管理", requiresAuth: true }
  },
  {
    path: "/tasks",
    name: "tasks",
    component: Tasks,
    meta: { title: "任务管理", requiresAuth: true }
  },
  {
    path: "/wallet",
    name: "wallet",
    component: Wallet,
    meta: { title: "钱包", requiresAuth: true }
  },
  {
    path: "/settings",
    name: "settings",
    component: Settings,
    meta: { title: "个人设置", requiresAuth: true }
  },
  {
    path: "/dev-tools",
    name: "dev-tools",
    component: DevTools,
    meta: { title: "研发工具", requiresAuth: true }
  },
  {
    path: "/prompts",
    name: "prompts",
    component: Prompts,
    meta: { title: "AI提示词", requiresAuth: true }
  },
  {
    path: "/content",
    name: "content",
    component: Content,
    meta: { title: "内容管理", requiresAuth: true }
  },
  {
    path: "/rss",
    name: "rss",
    component: RSS,
    meta: { title: "RSS管理", requiresAuth: true }
  },
  {
    path: "/solutions",
    name: "solutions",
    component: Solutions,
    meta: { title: "解决方案管理", requiresAuth: true }
  },
  {
    path: "/sidebar-demo",
    name: "sidebar-demo",
    component: SidebarDemo,
    meta: { title: "侧边栏演示", requiresAuth: true }
  },
  // 详情页面路由
  {
    path: "/services/:id",
    name: "service-detail",
    component: () => import("@views/mcp-service/ServiceDetail.vue").catch(err => {
      console.error('Failed to load ServiceDetailPage:', err)
      return import("@views/mcp-service/ServiceList.vue") // 降级处理
    }),
    meta: { title: "服务详情", requiresAuth: true }
  },
  {
    path: "/services/create",
    name: "service-create",
    component: () => import("@views/mcp-service/ServiceDetail.vue").catch(err => {
      console.error('Failed to load ServiceDetailPage:', err)
      return import("@views/mcp-service/ServiceList.vue") // 降级处理
    }),
    meta: { title: "新建服务", requiresAuth: true }
  },
  {
    path: "/prompts/:id",
    name: "prompt-detail",
    component: () => import("@views/prompt/PromptDetail.vue"),
    meta: { title: "提示词详情", requiresAuth: true }
  },
  {
    path: "/prompts/create",
    name: "prompt-create",
    component: () => import("@views/prompt/PromptDetail.vue"),
    meta: { title: "新建提示词", requiresAuth: true }
  },
  {
    path: "/content/:id",
    name: "content-detail",
    component: () => import("@views/content/ContentDetail.vue"),
    meta: { title: "内容详情", requiresAuth: true }
  },
  {
    path: "/content/create",
    name: "content-create",
    component: () => import("@views/content/ContentDetail.vue"),
    meta: { title: "新建内容", requiresAuth: true }
  },
  // Categories详情路由（如果需要独立页面）
  {
    path: "/categories/:id",
    name: "category-detail",
    component: () => import("@views/category/CategoryList.vue"), // 使用弹窗模式，重定向到列表页
    meta: { title: "分类详情", requiresAuth: true }
  },
  // Tags详情路由（如果需要独立页面）
  {
    path: "/tags/:id",
    name: "tag-detail",
    component: () => import("@views/tag/TagList.vue"), // 使用弹窗模式，重定向到列表页
    meta: { title: "标签详情", requiresAuth: true }
  },
  // Tasks详情路由（如果需要独立页面）
  {
    path: "/tasks/:id",
    name: "task-detail",
    component: () => import("@views/task/TaskList.vue"), // 使用弹窗模式，重定向到列表页
    meta: { title: "任务详情", requiresAuth: true }
  },
  // 404页面
  {
    path: "/:pathMatch(.*)*",
    name: "not-found",
    component: () => import("@views/system/NotFound.vue").catch(() => {
      // 如果NotFound.vue不存在，创建一个简单的404页面
      return {
        template: `
          <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <div class="text-center">
              <h1 class="text-6xl font-bold text-gray-900 dark:text-white">404</h1>
              <p class="text-xl text-gray-600 dark:text-gray-400 mt-4">页面未找到</p>
              <router-link to="/" class="mt-6 inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                返回首页
              </router-link>
            </div>
          </div>
        `
      }
    }),
    meta: { title: "页面未找到", requiresAuth: false }
  }

]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫 - 认证检查、权限验证和页面标题设置
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  try {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - Admin Dashboard`
    } else {
      document.title = 'Admin Dashboard'
    }

    // 如果是登录页面，且用户已登录，重定向到首页
    if (to.name === 'login' && userStore.isLoggedIn) {
      next('/')
      return
    }

    // 如果路由需要认证
    if (to.meta.requiresAuth !== false) {
      // 初始化用户状态（从localStorage恢复）
      if (!userStore.isLoggedIn && userStore.token) {
        await userStore.initUserState()
      }

      // 检查是否已登录
      if (!userStore.isLoggedIn) {
        // 保存当前路径，登录后可以重定向回来
        const redirectPath = to.fullPath !== '/login' ? to.fullPath : '/'
        next({
          path: '/login',
          query: { redirect: redirectPath }
        })
        return
      }

      // 检查用户权限（如果有权限系统）
      if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
        const hasPermission = to.meta.permissions.some(permission =>
          userStore.hasPermission && userStore.hasPermission(permission)
        )

        if (!hasPermission) {
          console.warn(`用户无权限访问页面: ${to.path}`)
          // 可以重定向到无权限页面或首页
          next('/')
          return
        }
      }
    }

    // 处理详情页面的特殊逻辑
    if (to.params.id && ['category-detail', 'tag-detail', 'task-detail', 'api-token-detail'].includes(to.name)) {
      // 对于使用弹窗模式的页面，重定向到列表页面并传递参数
      const listPageMap = {
        'category-detail': '/categories',
        'tag-detail': '/tags',
        'task-detail': '/tasks',
        'api-token-detail': '/api-tokens'
      }

      const listPage = listPageMap[to.name]
      if (listPage) {
        next({
          path: listPage,
          query: {
            ...to.query,
            openDetail: to.params.id,
            mode: to.query.mode || 'view'
          }
        })
        return
      }
    }

    next()
  } catch (error) {
    console.error('路由守卫执行错误:', error)
    // 发生错误时，重定向到安全页面
    if (to.path !== '/login' && to.path !== '/') {
      next('/')
    } else {
      next()
    }
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  // 可以在这里添加错误上报逻辑
})

// 路由后置守卫 - 页面加载完成后的处理
router.afterEach((to, from) => {
  // 页面切换完成后的处理
  // 可以在这里添加页面访问统计、埋点等逻辑

  // 滚动到页面顶部（除非有特殊的滚动位置要求）
  if (to.hash) {
    // 如果有锚点，滚动到指定位置
    setTimeout(() => {
      const element = document.querySelector(to.hash)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)
  } else {
    // 否则滚动到顶部
    window.scrollTo(0, 0)
  }
})

export default router
