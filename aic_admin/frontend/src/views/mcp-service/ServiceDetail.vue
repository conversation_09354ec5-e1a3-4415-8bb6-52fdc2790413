<template>
  <DetailPage
    :title="pageTitle"
    :description="pageDescription"
    :mode="mode"
    :data="serviceData"
    :tabs="tabs"
    :validation-rules="validationRules"
    :has-markdown-content="true"
    markdown-field="description"
    :auto-save-key="`service-${serviceId}`"
    @save="handleSave"
    @cancel="handleCancel"
    @data-change="handleDataChange"
  >
    <!-- 基本信息内容 -->
    <template #basic-content="{ data, errors, mode }">
      <ServiceDetail
        v-model="serviceData"
        :mode="mode"
        :errors="errors"
        active-tab="basic"
      />
    </template>

    <!-- 配置参数内容 -->
    <template #config-content="{ data, errors, mode }">
      <ServiceDetail
        v-model="serviceData"
        :mode="mode"
        :errors="errors"
        active-tab="config"
      />
    </template>

    <!-- 历史版本内容 -->
    <template #history-content="{ data, mode }">
      <div class="history-section">
        <div v-if="versionHistory.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3 class="empty-title">暂无版本历史</h3>
          <p class="empty-description">当前服务还没有版本历史记录</p>
        </div>
        
        <div v-else class="version-list">
          <div
            v-for="version in versionHistory"
            :key="version.id"
            class="version-item"
          >
            <div class="version-header">
              <div class="version-info">
                <span class="version-number">{{ version.version }}</span>
                <span class="version-date">{{ formatDate(version.createdAt) }}</span>
              </div>
              <div class="version-actions">
                <button class="action-btn view-btn" @click="viewVersion(version)">
                  查看
                </button>
                <button class="action-btn restore-btn" @click="restoreVersion(version)">
                  恢复
                </button>
              </div>
            </div>
            <div class="version-changes">
              <p class="change-summary">{{ version.changeLog }}</p>
              <div class="change-author">
                <span>修改人：{{ version.author }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 侧边栏内容 -->
    <template #sidebar="{ data, toc }">
      <div class="sidebar-section">
        <h4 class="sidebar-title">快速信息</h4>
        <div class="quick-info">
          <div class="info-item">
            <span class="info-label">服务ID</span>
            <span class="info-value">{{ serviceId }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间</span>
            <span class="info-value">{{ formatDate(data.createdAt) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">最后更新</span>
            <span class="info-value">{{ formatDate(data.updatedAt) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">状态</span>
            <span :class="['status-badge', data.status]">
              {{ getStatusLabel(data.status) }}
            </span>
          </div>
        </div>
      </div>
    </template>
  </DetailPage>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DetailPage from '@/components/ui/DetailPage.vue'
import ServiceDetail from '@/components/mcp-service/ServiceForm.vue'
import { getTemplateConfig, getValidationRules } from '@/config/detailTemplates'
import { knowledgeApi, post } from '@/utils/api'
import { useToast } from '@/composables/useToast'

const route = useRoute()
const router = useRouter()
const toast = useToast()

// 状态
const serviceData = ref({
  name: '',
  category: '',
  version: '',
  status: '',
  tags: [],
  summary: '',
  description: '',
  endpoint: '',
  timeout: 30,
  retries: 3,
  config: {},
  createdAt: '',
  updatedAt: ''
})

const versionHistory = ref([])
const loading = ref(false)

// 计算属性
const serviceId = computed(() => route.params.id)
const mode = computed(() => {
  if (route.name === 'service-create') return 'create'
  if (route.query.mode === 'edit') return 'edit'
  return 'view'
})

const pageTitle = computed(() => {
  if (mode.value === 'create') return '新建服务'
  if (mode.value === 'edit') return `编辑服务 - ${serviceData.value.name}`
  return `服务详情 - ${serviceData.value.name}`
})

const pageDescription = computed(() => {
  if (mode.value === 'create') return '创建新的服务配置'
  return '查看和管理服务的详细信息'
})

// 获取模板配置
const templateConfig = getTemplateConfig('services')
const tabs = templateConfig.tabs
const validationRules = getValidationRules('services', 'basic')

// 方法
const loadServiceData = async () => {
  if (mode.value === 'create') {
    // 新建模式，使用默认数据
    serviceData.value = {
      name: '',
      category: '',
      version: 'v1.0.0',
      status: 'draft',
      tags: [],
      summary: '',
      description: '# 服务文档\n\n请在此处编写服务的详细文档...',
      endpoint: '',
      timeout: 30,
      retries: 3,
      config: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return
  }

  // 模拟加载数据
  loading.value = true
  try {
    // 这里应该调用API获取服务数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    serviceData.value = {
      id: serviceId.value,
      name: '图像超分辨率服务',
      category: 'api',
      version: 'v2.6.0',
      status: 'active',
      tags: ['API', '图像处理', '机器学习'],
      summary: '基于深度学习的图像超分辨率处理服务',
      description: `# 图像超分辨率服务

## 服务概述

本服务提供基于深度学习的图像超分辨率处理功能，能够将低分辨率图像提升到高分辨率，同时保持图像质量。

## 主要特性

- 支持多种图像格式（JPEG、PNG、WebP等）
- 可配置的放大倍数（2x、4x、8x）
- 批量处理支持
- 实时处理API

## 使用方法

\\\`\\\`\\\`bash
curl -X POST https://api.example.com/upscale \\\\
  -H "Content-Type: application/json" \\\\
  -d '{"image_url": "https://example.com/image.jpg", "scale": 4}'
\\\`\\\`\\\`

## 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| image_url | string | 是 | 图像URL |
| scale | number | 否 | 放大倍数，默认2 |
| format | string | 否 | 输出格式，默认jpeg |
`,
      endpoint: 'https://api.example.com/upscale',
      timeout: 60,
      retries: 3,
      config: {
        max_file_size: '10MB',
        supported_formats: ['jpeg', 'png', 'webp'],
        default_scale: 2,
        max_scale: 8
      },
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z'
    }

    // 加载版本历史
    versionHistory.value = [
      {
        id: 1,
        version: 'v2.6.0',
        changeLog: '优化算法性能，提升处理速度30%',
        author: '张三',
        createdAt: '2024-01-15T14:30:00Z'
      },
      {
        id: 2,
        version: 'v2.5.0',
        changeLog: '新增WebP格式支持，修复批量处理bug',
        author: '李四',
        createdAt: '2024-01-10T09:15:00Z'
      },
      {
        id: 3,
        version: 'v2.4.0',
        changeLog: '增加8x放大倍数支持',
        author: '王五',
        createdAt: '2024-01-05T16:45:00Z'
      }
    ]
  } catch (error) {
    console.error('加载服务数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSave = async (data) => {
  loading.value = true

  try {
    console.log('开始保存服务数据:', data)

    if (mode.value === 'create') {
      // 新建模式：调用 KnowledgeService#createKnowledge 方法
      await createKnowledge(data)
    } else {
      // 编辑模式：调用更新接口
      await updateKnowledge(data)
    }

  } catch (error) {
    console.error('保存失败:', error)
    toast.error(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 创建知识（新建模式）
const createKnowledge = async (data) => {
  try {
    // 构建符合 CreateKnowledgeRequest 格式的请求参数
    const createRequest = {
      knowledgeTypeId: 1, // MCP 服务的知识类型ID，需要根据实际情况调整
      title: data.name || '未命名服务',
      description: data.summary || '',
      content: data.description || '',
      summary: data.summary || '',
      metadata: {
        // MCP 服务特有的元数据
        category: data.category || '',
        version: data.version || 'v1.0.0',
        status: data.status || 'draft',
        tags: data.tags || [],
        endpoint: data.endpoint || '',
        timeout: data.timeout || 30,
        retries: data.retries || 3,
        config: data.config || {}
      },
      visibility: 'PUBLIC', // 可见性：PUBLIC, PRIVATE, TEAM
      tags: data.tags || []
    }

    console.log('调用 /api/knowledge/create 接口，参数:', createRequest)

    // 调用后台 KnowledgeController 的创建接口
    const response = await post('/knowledge/create', createRequest)

    console.log('创建知识响应:', response)

    // 判断返回值
    if (!response) {
      throw new Error('接口无响应')
    }

    if (response.code !== 200) {
      const errorMessage = response.message || `创建失败，错误码: ${response.code}`
      throw new Error(errorMessage)
    }

    if (!response.data) {
      throw new Error('创建成功但返回数据为空')
    }

    // 创建成功
    const createdKnowledge = response.data
    console.log('✅ 知识创建成功:', createdKnowledge)

    toast.success(`服务 "${data.name}" 创建成功`)

    // 跳转到详情页
    router.push({
      name: 'service-detail',
      params: { id: createdKnowledge.id || 'new-service' }
    })

  } catch (error) {
    console.error('❌ 创建知识失败:', error)
    throw error
  }
}

// 更新知识（编辑模式）
const updateKnowledge = async (data) => {
  try {
    // 构建更新请求参数
    const updateRequest = {
      title: data.name || '未命名服务',
      description: data.summary || '',
      content: data.description || '',
      summary: data.summary || '',
      metadata: {
        category: data.category || '',
        version: data.version || 'v1.0.0',
        status: data.status || 'draft',
        tags: data.tags || [],
        endpoint: data.endpoint || '',
        timeout: data.timeout || 30,
        retries: data.retries || 3,
        config: data.config || {}
      },
      tags: data.tags || []
    }

    console.log('调用 /api/knowledge/{id} 更新接口，参数:', updateRequest)

    // 调用后台更新接口
    const response = await knowledgeApi.updateKnowledge(serviceId.value, updateRequest)

    console.log('更新知识响应:', response)

    // 判断返回值
    if (!response) {
      throw new Error('接口无响应')
    }

    if (response.code !== 200) {
      const errorMessage = response.message || `更新失败，错误码: ${response.code}`
      throw new Error(errorMessage)
    }

    // 更新成功
    console.log('✅ 知识更新成功:', response.data)

    toast.success(`服务 "${data.name}" 更新成功`)

    // 切换到查看模式
    router.push({
      name: 'service-detail',
      params: { id: serviceId.value }
    })

  } catch (error) {
    console.error('❌ 更新知识失败:', error)
    throw error
  }
}

const handleCancel = () => {
  router.push({ name: 'services' })
}

const handleDataChange = (data) => {
  serviceData.value = data
}

const viewVersion = (version) => {
  console.log('查看版本:', version)
  // 这里可以打开版本对比弹窗
}

const restoreVersion = (version) => {
  console.log('恢复版本:', version)
  // 这里可以恢复到指定版本
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const getStatusLabel = (status) => {
  const statusMap = {
    active: '已发布',
    draft: '草稿',
    deprecated: '已废弃'
  }
  return statusMap[status] || status
}

// 监听路由变化，只在必要时重新加载数据
watch([() => route.params.id, () => route.query.mode], ([newId, newMode], [oldId, oldMode]) => {
  // 只有当ID变化或从创建模式切换到其他模式时才重新加载
  if (newId !== oldId || (oldMode === undefined && newMode !== undefined)) {
    loadServiceData()
  }
}, { immediate: true })

onMounted(() => {
  // 初始加载已经通过watch处理，这里不需要重复调用
  // loadServiceData()
})
</script>

<style scoped>
/* 历史版本部分 */
.history-section {
  @apply space-y-4;
}

.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-6xl mb-4;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-description {
  @apply text-gray-600 dark:text-gray-400;
}

.version-list {
  @apply space-y-4;
}

.version-item {
  @apply bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700;
}

.version-header {
  @apply flex items-center justify-between mb-3;
}

.version-info {
  @apply flex items-center gap-3;
}

.version-number {
  @apply font-semibold text-blue-600 dark:text-blue-400;
}

.version-date {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.version-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply px-3 py-1 text-sm rounded transition-colors;
}

.view-btn {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
  @apply hover:bg-blue-200 dark:hover:bg-blue-800;
}

.restore-btn {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
  @apply hover:bg-green-200 dark:hover:bg-green-800;
}

.version-changes {
  @apply space-y-2;
}

.change-summary {
  @apply text-gray-900 dark:text-white;
}

.change-author {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 侧边栏部分 */
.sidebar-section {
  @apply p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3;
}

.quick-info {
  @apply space-y-3;
}

.info-item {
  @apply flex flex-col gap-1;
}

.info-label {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.info-value {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.status-badge {
  @apply px-2 py-1 text-xs rounded-full font-medium;
}

.status-badge.active {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.status-badge.draft {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
}

.status-badge.deprecated {
  @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
}
</style>