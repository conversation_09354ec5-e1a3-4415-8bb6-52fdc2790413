<template>
  <div :class="['service-detail-dialog', { 'dark-mode': isDarkMode }]">
    <!-- 对话框头部 -->
    <div class="dialog-header">
      <div class="header-content">
        <div class="header-icon">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
        </div>
        <div class="header-text">
          <h2 class="header-title">{{ pageTitle }}</h2>
          <p class="header-description">{{ pageDescription }}</p>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="tabs-nav">
      <div class="tabs-container">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          :class="[
            'tab-button',
            { 'active': activeTab === tab.key }
          ]"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-label">{{ tab.label }}</span>
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="dialog-content">
      <!-- 基本信息标签页 -->
      <div v-show="activeTab === 'basic'" class="tab-content">
        <div class="content-card">
          <div class="card-header">
            <div class="card-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div class="card-title-section">
              <h3 class="card-title">基本信息</h3>
              <p class="card-subtitle">配置服务的基本属性和描述信息</p>
            </div>
          </div>
          <div class="card-content">
            <ServiceDetail
              v-model="serviceData"
              :mode="mode"
              :errors="formErrors"
              active-tab="basic"
            />
          </div>
        </div>
      </div>

      <!-- 配置参数标签页 -->
      <div v-show="activeTab === 'config'" class="tab-content">
        <div class="content-card">
          <div class="card-header">
            <div class="card-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <div class="card-title-section">
              <h3 class="card-title">配置参数</h3>
              <p class="card-subtitle">设置服务的运行参数和高级选项</p>
            </div>
          </div>
          <div class="card-content">
            <ServiceDetail
              v-model="serviceData"
              :mode="mode"
              :errors="formErrors"
              active-tab="config"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="dialog-footer">
      <div class="footer-actions">
        <button
          @click="handleCancel"
          :disabled="loading"
          class="btn-secondary"
        >
          取消
        </button>
        <button
          @click="handleSave"
          :disabled="loading || !isFormValid"
          class="btn-primary"
        >
          <svg v-if="loading" class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a7.646 7.646 0 100 15.292V12"></path>
          </svg>
          {{ loading ? '创建中...' : '创建服务' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ServiceDetail from './ServiceForm.vue'
import { useThemeStore } from '@/stores/theme'

const props = defineProps({
  mode: {
    type: String,
    default: 'create'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save', 'cancel'])

// 主题状态
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 当前活跃的标签页
const activeTab = ref('basic')

// 表单错误
const formErrors = ref({})

// 服务数据
const serviceData = ref({
  // 基本信息字段（参考ServiceEdit）
  title: '',
  description: '',
  knowledgeTypeId: 1, // MCP 服务的知识类型ID
  status: 'draft',
  visibility: 'PUBLIC',
  content: '',

  // 原有字段保留
  name: '',
  category: '',
  version: 'v1.0.0',
  summary: '',
  endpoint: '',
  timeout: 30,
  retries: 3,
  tags: [],
  config: {}
})

// 页面配置
const pageTitle = computed(() => {
  return '新增 MCP 服务'
})

const pageDescription = computed(() => {
  return '创建新的 MCP 服务配置'
})

// 标签页配置
const tabs = ref([
  {
    key: 'basic',
    label: '基本信息',
    icon: '📋'
  },
  {
    key: 'config',
    label: '配置参数',
    icon: '⚙️'
  }
])

// 表单验证
const isFormValid = computed(() => {
  return serviceData.value.title &&
         serviceData.value.title.trim() !== '' &&
         serviceData.value.description &&
         serviceData.value.description.trim() !== '' &&
         serviceData.value.knowledgeTypeId &&
         serviceData.value.status &&
         serviceData.value.visibility &&
         serviceData.value.content &&
         serviceData.value.content.trim() !== ''
})

// 处理保存
const handleSave = async () => {
  emit('save', serviceData.value)
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
/* 对话框主容器 */
.service-detail-dialog {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  overflow: hidden;
}

/* 对话框头部 */
.dialog-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
  background: linear-gradient(135deg, #ffffff 0%, #dbeafe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  color: #dbeafe;
}

/* 标签页导航 */
.tabs-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 24px;
}

.tabs-container {
  display: flex;
  gap: 8px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button:hover {
  color: #3b82f6;
  background: #eff6ff;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.tab-icon {
  font-size: 16px;
}

.tab-label {
  font-weight: 600;
}

/* 内容区域 */
.dialog-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: white;
}

.tab-content {
  height: 100%;
}

.content-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;
}

.content-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.card-header {
  background: linear-gradient(135deg, #f9fafb 0%, #eff6ff 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.card-title-section {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 4px 0;
}

.card-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.card-content {
  padding: 24px;
}

/* 底部操作区域 */
.dialog-footer {
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 20px 24px;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 按钮样式 */
.btn-secondary {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  padding: 10px 20px;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 深色模式支持 */
.service-detail-dialog.dark-mode {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.service-detail-dialog.dark-mode .tabs-nav {
  background: #1f2937;
  border-bottom-color: #374151;
}

.service-detail-dialog.dark-mode .tab-button {
  color: #9ca3af;
}

.service-detail-dialog.dark-mode .tab-button:hover,
.service-detail-dialog.dark-mode .tab-button.active {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.service-detail-dialog.dark-mode .dialog-content {
  background: #1f2937;
}

.service-detail-dialog.dark-mode .content-card {
  background: #1f2937;
  border-color: #374151;
}

.service-detail-dialog.dark-mode .card-header {
  background: linear-gradient(135deg, #374151 0%, rgba(59, 130, 246, 0.1) 100%);
  border-bottom-color: #374151;
}

.service-detail-dialog.dark-mode .card-title {
  color: #f9fafb;
}

.service-detail-dialog.dark-mode .card-subtitle {
  color: #9ca3af;
}

.service-detail-dialog.dark-mode .dialog-footer {
  background: #374151;
  border-top-color: #4b5563;
}

.service-detail-dialog.dark-mode .btn-secondary {
  background: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

.service-detail-dialog.dark-mode .btn-secondary:hover {
  background: #4b5563;
  border-color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .tabs-nav {
    padding: 0 16px;
  }

  .tabs-container {
    flex-direction: column;
    gap: 0;
  }

  .tab-button {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 0;
  }

  .service-detail-dialog.dark-mode .tab-button {
    border-bottom-color: #374151;
  }

  .dialog-content {
    padding: 16px;
  }

  .card-header {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .card-content {
    padding: 16px;
  }

  .dialog-footer {
    padding: 16px;
  }

  .footer-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
    justify-content: center;
  }
}

/* 移除不必要的样式覆盖 */
:deep(.service-form) {
  margin: 0;
  padding: 0;
}

:deep(.form-container) {
  padding: 0;
  margin: 0;
}
</style>
