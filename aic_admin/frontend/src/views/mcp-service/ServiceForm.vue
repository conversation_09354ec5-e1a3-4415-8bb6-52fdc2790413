<template>
  <div class="service-detail">
    <!-- 基本信息内容 -->
    <template v-if="activeTab === 'basic'">
      <div class="form-grid">
        <!-- 标题字段 -->
        <div class="form-group">
          <label class="form-label">标题 *</label>
          <input
            v-model="localData.title"
            type="text"
            class="form-input"
            :class="{ 'error': errors.title }"
            :readonly="mode === 'view'"
            placeholder="请输入服务标题"
          />
          <span v-if="errors.title" class="error-message">{{ errors.title }}</span>
        </div>

        <!-- 知识类型字段 -->
        <div class="form-group">
          <label class="form-label">知识类型 *</label>
          <select
            v-model="localData.knowledgeTypeId"
            class="form-select"
            :class="{ 'error': errors.knowledgeTypeId }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择知识类型</option>
            <option value="1">MCP服务</option>
            <option value="2">API文档</option>
            <option value="3">技术方案</option>
            <option value="4">操作手册</option>
          </select>
          <span v-if="errors.knowledgeTypeId" class="error-message">{{ errors.knowledgeTypeId }}</span>
        </div>

        <!-- 状态字段 -->
        <div class="form-group">
          <label class="form-label">状态 *</label>
          <select
            v-model="localData.status"
            class="form-select"
            :class="{ 'error': errors.status }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择状态</option>
            <option value="draft">草稿</option>
            <option value="published">已发布</option>
            <option value="archived">已归档</option>
            <option value="deleted">已删除</option>
          </select>
          <span v-if="errors.status" class="error-message">{{ errors.status }}</span>
        </div>

        <!-- 可见性字段 -->
        <div class="form-group">
          <label class="form-label">可见性 *</label>
          <select
            v-model="localData.visibility"
            class="form-select"
            :class="{ 'error': errors.visibility }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择可见性</option>
            <option value="PUBLIC">公开</option>
            <option value="PRIVATE">私有</option>
            <option value="TEAM">团队可见</option>
          </select>
          <span v-if="errors.visibility" class="error-message">{{ errors.visibility }}</span>
        </div>

        <!-- 描述字段 -->
        <div class="form-group full-width">
          <label class="form-label">描述 *</label>
          <textarea
            v-model="localData.description"
            class="form-textarea"
            :class="{ 'error': errors.description }"
            :readonly="mode === 'view'"
            rows="3"
            placeholder="请输入服务描述"
          ></textarea>
          <span v-if="errors.description" class="error-message">{{ errors.description }}</span>
        </div>

        <!-- 内容字段 -->
        <div class="form-group full-width">
          <label class="form-label">内容 *</label>
          <textarea
            v-model="localData.content"
            class="form-textarea"
            :class="{ 'error': errors.content }"
            :readonly="mode === 'view'"
            rows="8"
            placeholder="请输入详细内容，支持Markdown格式"
          ></textarea>
          <span v-if="errors.content" class="error-message">{{ errors.content }}</span>
        </div>
      </div>
    </template>

    <!-- 配置参数内容 -->
    <template v-if="activeTab === 'config'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">API端点</label>
          <input
            v-model="localData.endpoint"
            type="url"
            class="form-input"
            :class="{ 'error': errors.endpoint }"
            :readonly="mode === 'view'"
            placeholder="https://api.example.com"
          />
          <span v-if="errors.endpoint" class="error-message">{{ errors.endpoint }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">超时时间(秒)</label>
          <input
            v-model.number="localData.timeout"
            type="number"
            class="form-input"
            :readonly="mode === 'view'"
            min="1"
            max="300"
            placeholder="30"
          />
        </div>

        <div class="form-group">
          <label class="form-label">重试次数</label>
          <input
            v-model.number="localData.retries"
            type="number"
            class="form-input"
            :readonly="mode === 'view'"
            min="0"
            max="10"
            placeholder="3"
          />
        </div>

        <div class="form-group full-width">
          <label class="form-label">配置参数 (JSON)</label>
          <textarea
            v-model="configJson"
            class="form-textarea json-editor"
            :readonly="mode === 'view'"
            rows="10"
            placeholder='{"key": "value"}'
            @blur="validateJson"
          ></textarea>
          <span v-if="jsonError" class="error-message">{{ jsonError }}</span>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  activeTab: {
    type: String,
    default: 'basic'
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('ServiceDetail')

// 本地数据
const localData = ref({
  // 新增的基本信息字段
  title: '',
  description: '',
  knowledgeTypeId: 1,
  status: '',
  visibility: 'PUBLIC',
  content: '',

  // 原有字段保留
  name: '',
  category: '',
  version: '',
  tags: [],
  summary: '',
  endpoint: '',
  timeout: 30,
  retries: 3,
  config: {},
  ...props.modelValue
})

// JSON配置相关
const configJson = ref('')
const jsonError = ref('')

// 初始化JSON配置
if (localData.value.config && typeof localData.value.config === 'object') {
  configJson.value = JSON.stringify(localData.value.config, null, 2)
}

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    if (newValue.config && typeof newValue.config === 'object') {
      configJson.value = JSON.stringify(newValue.config, null, 2)
    }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法

const validateJson = () => {
  jsonError.value = ''
  if (!configJson.value.trim()) {
    localData.value.config = {}
    return
  }

  try {
    const parsed = JSON.parse(configJson.value)
    localData.value.config = parsed
  } catch (error) {
    jsonError.value = 'JSON格式错误: ' + error.message
  }
}
</script>

<style scoped>
.service-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply md:col-span-2;
}

/* 只读字段样式 */
.form-input[readonly],
.form-textarea[readonly],
.form-select[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-not-allowed;
  border-color: #e5e7eb;
}

.dark .form-input[readonly],
.dark .form-textarea[readonly],
.dark .form-select[disabled] {
  border-color: #374151;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

.tags-input {
  @apply border border-gray-300 dark:border-gray-600 rounded-md p-2;
  @apply bg-white dark:bg-gray-700;
}

.tags-list {
  @apply flex flex-wrap gap-2 mb-2;
}

.tag-item {
  @apply inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900;
  @apply text-blue-800 dark:text-blue-200 text-sm rounded;
}

.tag-remove {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200;
  @apply font-bold;
}

.tag-input {
  @apply w-full border-0 outline-none bg-transparent;
  @apply text-gray-900 dark:text-white;
}

.json-editor {
  @apply font-mono text-sm;
}
</style>
