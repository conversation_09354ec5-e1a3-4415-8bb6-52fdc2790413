<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          {{ mode === 'create' ? '新建解决方案' : '编辑解决方案' }}
        </h2>
        <button @click="$emit('close')" class="close-button">
          <i class="icon-close"></i>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit" class="solution-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <div class="form-group">
              <label class="form-label required">解决方案标题</label>
              <input
                v-model="formData.title"
                type="text"
                class="form-input"
                placeholder="请输入解决方案标题"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">解决方案描述</label>
              <textarea
                v-model="formData.description"
                class="form-textarea"
                rows="3"
                placeholder="请输入解决方案的简要描述"
              ></textarea>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">分类</label>
                <select v-model="formData.category" class="form-select">
                  <option value="">请选择分类</option>
                  <option value="技术问题">技术问题</option>
                  <option value="业务场景">业务场景</option>
                  <option value="工具使用">工具使用</option>
                  <option value="最佳实践">最佳实践</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">状态</label>
                <select v-model="formData.status" class="form-select">
                  <option :value="0">草稿</option>
                  <option :value="1">已发布</option>
                  <option :value="2">已下线</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">标签</label>
              <div class="tags-input">
                <div class="tag-list">
                  <span
                    v-for="(tag, index) in formData.tags"
                    :key="index"
                    class="tag-item"
                  >
                    {{ tag }}
                    <button
                      type="button"
                      @click="removeTag(index)"
                      class="tag-remove"
                    >
                      ×
                    </button>
                  </span>
                </div>
                <input
                  v-model="newTag"
                  @keydown.enter.prevent="addTag"
                  @keydown.comma.prevent="addTag"
                  type="text"
                  class="tag-input"
                  placeholder="输入标签后按回车添加"
                />
              </div>
            </div>
          </div>

          <!-- 详细内容 -->
          <div class="form-section">
            <h3 class="section-title">详细内容</h3>
            
            <div class="form-group">
              <label class="form-label">解决方案内容</label>
              <div class="editor-container">
                <div class="editor-toolbar">
                  <button type="button" class="toolbar-btn" title="加粗">
                    <i class="icon-bold"></i>
                  </button>
                  <button type="button" class="toolbar-btn" title="斜体">
                    <i class="icon-italic"></i>
                  </button>
                  <button type="button" class="toolbar-btn" title="代码">
                    <i class="icon-code"></i>
                  </button>
                  <button type="button" class="toolbar-btn" title="链接">
                    <i class="icon-link"></i>
                  </button>
                </div>
                <textarea
                  v-model="formData.content"
                  class="form-textarea editor-textarea"
                  rows="10"
                  placeholder="请输入解决方案的详细内容，支持Markdown格式"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- 元数据 -->
          <div class="form-section">
            <h3 class="section-title">其他信息</h3>
            
            <div class="form-group">
              <label class="form-label">封面图片URL</label>
              <input
                v-model="formData.coverImageUrl"
                type="url"
                class="form-input"
                placeholder="请输入封面图片的URL地址"
              />
            </div>

            <div class="form-group">
              <label class="form-label">可见性</label>
              <div class="radio-group">
                <label class="radio-item">
                  <input
                    v-model="formData.visibility"
                    type="radio"
                    :value="0"
                  />
                  <span class="radio-label">私有</span>
                </label>
                <label class="radio-item">
                  <input
                    v-model="formData.visibility"
                    type="radio"
                    :value="1"
                  />
                  <span class="radio-label">团队可见</span>
                </label>
                <label class="radio-item">
                  <input
                    v-model="formData.visibility"
                    type="radio"
                    :value="2"
                  />
                  <span class="radio-label">公开</span>
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          @click="$emit('close')"
          class="btn btn-outline"
          :disabled="saving"
        >
          取消
        </button>
        <button
          type="button"
          @click="handleSubmit"
          class="btn btn-primary"
          :disabled="saving || !isFormValid"
        >
          <span v-if="saving" class="loading-spinner"></span>
          {{ saving ? '保存中...' : (mode === 'create' ? '创建' : '保存') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { useCrudOperations } from '@/composables/useCrudOperations'

export default {
  name: 'SolutionEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    solution: {
      type: Object,
      default: null
    },
    mode: {
      type: String,
      default: 'create', // 'create' | 'edit'
      validator: (value) => ['create', 'edit'].includes(value)
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const { create, update } = useCrudOperations('solution')
    
    const saving = ref(false)
    const newTag = ref('')
    
    // 表单数据
    const formData = reactive({
      title: '',
      description: '',
      category: '',
      status: 0,
      content: '',
      tags: [],
      coverImageUrl: '',
      visibility: 2, // 默认公开
      authorId: null,
      authorName: ''
    })

    // 计算属性
    const isFormValid = computed(() => {
      return formData.title && formData.title.trim().length > 0
    })

    // 监听props变化，初始化表单数据
    watch(() => props.solution, (newSolution) => {
      if (newSolution && props.mode === 'edit') {
        Object.assign(formData, {
          ...newSolution,
          tags: newSolution.tags || []
        })
      } else {
        // 重置表单
        Object.assign(formData, {
          title: '',
          description: '',
          category: '',
          status: 0,
          content: '',
          tags: [],
          coverImageUrl: '',
          visibility: 2,
          authorId: null,
          authorName: ''
        })
      }
    }, { immediate: true })

    // 方法
    const handleOverlayClick = () => {
      emit('close')
    }

    const addTag = () => {
      const tag = newTag.value.trim()
      if (tag && !formData.tags.includes(tag)) {
        formData.tags.push(tag)
        newTag.value = ''
      }
    }

    const removeTag = (index) => {
      formData.tags.splice(index, 1)
    }

    const handleSubmit = async () => {
      if (!isFormValid.value || saving.value) return

      saving.value = true
      try {
        let result
        if (props.mode === 'create') {
          result = await create(formData)
        } else {
          result = await update(formData.id, formData)
        }

        if (result) {
          emit('success', result)
        }
      } catch (error) {
        console.error('保存失败:', error)
      } finally {
        saving.value = false
      }
    }

    return {
      formData,
      saving,
      newTag,
      isFormValid,
      handleOverlayClick,
      addTag,
      removeTag,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.solution-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-label.required::after {
  content: ' *';
  color: #ef4444;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.tags-input {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px;
  min-height: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  background: #eff6ff;
  color: #1d4ed8;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  gap: 4px;
}

.tag-remove {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}

.tag-remove:hover {
  background: rgba(0, 0, 0, 0.1);
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 100px;
  padding: 4px;
  font-size: 14px;
}

.editor-container {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 12px;
  display: flex;
  gap: 4px;
}

.toolbar-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.editor-textarea {
  border: none;
  border-radius: 0;
  resize: vertical;
  min-height: 200px;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.radio-label {
  font-size: 14px;
  color: #374151;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
